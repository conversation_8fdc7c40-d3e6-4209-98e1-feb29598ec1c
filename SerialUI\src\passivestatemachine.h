#ifndef PASSIVESTATEMACHINE_H
#define PASSIVESTATEMACHINE_H

class PassiveStateMachine
{
public:
    enum class State
    {
        IDLE,
        P_WAITING_FOR_AGV_REQUEST_CMD,   // Got CS0, VALID
        P_PREPARING_TRANSFER,            // Got L_REQ/TR_REQ CMD
        P_READY_WAITING_FOR_AGV_BUSY,    // Sent PASSIVE_READY_STATUS ON
        P_MONITORING_TRANSFER,           // AGV is BUSY
        P_WAITING_FOR_FINAL_AGV_CLEANUP, // Sent PASSIVE_TRANSFER_COMPLETED & PASSIVE_READY_OFF
        P_ERROR_STATE
    };

private:
    State current_state_;
    const int timeout_ = 1000;

public:
    PassiveStateMachine();
    ~PassiveStateMachine();

    State getCurrentState() const{ return current_state_; };
    string getCurrentStateName() const{ switch(current_state_) {
        case State::IDLE: return "IDLE";
        case State::P_WAITING_FOR_AGV_REQUEST_CMD: return "P_WAITING_FOR_AGV_REQUEST_CMD";
        case State::P_PREPARING_TRANSFER: return "P_PREPARING_TRANSFER";
        case State::P_READY_WAITING_FOR_AGV_BUSY: return "P_READY_WAITING_FOR_AGV_BUSY";
        case State::P_MONITORING_TRANSFER: return "P_MONITORING_TRANSFER";
        case State::P_WAITING_FOR_FINAL_AGV_CLEANUP: return "P_WAITING_FOR_FINAL_AGV_CLEANUP";
        case State::P_ERROR_STATE: return "P_ERROR_STATE";
        default: return "UNKNOWN_PASSIVE_STATE";
    }

    };
};

#endif // PASSIVESTATEMACHINE_H
